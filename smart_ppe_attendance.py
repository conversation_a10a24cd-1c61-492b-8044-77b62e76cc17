"""
Smart PPE Attendance Engine
Combines PPE detection with face recognition for intelligent attendance tracking
"""

import logging
import time
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AccessDecision(Enum):
    """Access decision types"""
    GRANTED = "granted"
    DENIED = "denied"
    UNKNOWN_PERSON = "unknown_person"
    NO_FACE_DETECTED = "no_face_detected"


@dataclass
class PPERequirement:
    """PPE requirement configuration"""
    helmet_required: bool = True
    mask_required: bool = True
    vest_required: bool = True
    
    def get_required_items(self) -> List[str]:
        """Get list of required PPE items"""
        required = []
        if self.helmet_required:
            required.append("Hardhat")
        if self.mask_required:
            required.append("Mask")
        if self.vest_required:
            required.append("Safety Vest")
        return required


@dataclass
class SmartAttendanceResult:
    """Result of smart attendance processing"""
    person_name: str
    person_id: Optional[str]
    face_confidence: float
    access_decision: AccessDecision
    ppe_status: Dict[str, bool]  # PPE item -> present/absent
    missing_ppe: List[str]
    message: str
    timestamp: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'person_name': self.person_name,
            'person_id': self.person_id,
            'face_confidence': self.face_confidence,
            'access_decision': self.access_decision.value,
            'ppe_status': self.ppe_status,
            'missing_ppe': self.missing_ppe,
            'message': self.message,
            'timestamp': self.timestamp
        }


class SmartPPEAttendanceEngine:
    """
    Smart PPE Attendance Engine
    
    Combines face recognition with PPE detection to make intelligent
    attendance decisions based on both identity and safety compliance.
    """
    
    def __init__(self, ppe_requirements: Optional[PPERequirement] = None):
        """Initialize the Smart PPE Attendance Engine
        
        Args:
            ppe_requirements: PPE requirements configuration
        """
        self.ppe_requirements = ppe_requirements or PPERequirement()
        self.enabled = False
        self.recent_decisions = []
        self.decision_history = {}  # person_id -> list of recent decisions
        self.cooldown_period = 30  # seconds between decisions for same person
        
        # Statistics tracking
        self.stats = {
            'total_decisions': 0,
            'access_granted': 0,
            'access_denied': 0,
            'unknown_persons': 0,
            'session_start': time.time()
        }
        
        logger.info("Smart PPE Attendance Engine initialized")
    
    def set_enabled(self, enabled: bool):
        """Enable or disable smart attendance mode"""
        self.enabled = enabled
        if enabled:
            logger.info("Smart PPE Attendance Mode ENABLED")
        else:
            logger.info("Smart PPE Attendance Mode DISABLED")
    
    def is_enabled(self) -> bool:
        """Check if smart attendance mode is enabled"""
        return self.enabled
    
    def update_ppe_requirements(self, requirements: PPERequirement):
        """Update PPE requirements"""
        self.ppe_requirements = requirements
        logger.info(f"PPE requirements updated: {requirements.get_required_items()}")
    
    def _analyze_ppe_compliance(self, detections: List[Dict]) -> Tuple[Dict[str, bool], List[str]]:
        """Analyze PPE compliance from detection results
        
        Args:
            detections: List of detection results from PPE engine
            
        Returns:
            Tuple of (ppe_status_dict, missing_ppe_list)
        """
        # Initialize PPE status - assume all required items are missing
        ppe_status = {
            "Hardhat": False,
            "Mask": False,
            "Safety Vest": False
        }
        
        # Check detections for PPE items
        for detection in detections:
            class_name = detection.get('class_name', '')
            confidence = detection.get('confidence', 0)
            
            # Only consider high-confidence detections
            if confidence > 0.5:
                if class_name == 'Hardhat':
                    ppe_status["Hardhat"] = True
                elif class_name == 'Mask':
                    ppe_status["Mask"] = True
                elif class_name == 'Safety Vest':
                    ppe_status["Safety Vest"] = True
        
        # Determine missing PPE based on requirements
        missing_ppe = []
        required_items = self.ppe_requirements.get_required_items()
        
        for item in required_items:
            if not ppe_status.get(item, False):
                missing_ppe.append(item)
        
        return ppe_status, missing_ppe
    
    def _check_cooldown(self, person_id: str) -> bool:
        """Check if person is in cooldown period
        
        Args:
            person_id: Person identifier
            
        Returns:
            True if in cooldown, False otherwise
        """
        if person_id not in self.decision_history:
            return False
        
        recent_decisions = self.decision_history[person_id]
        if not recent_decisions:
            return False
        
        last_decision_time = recent_decisions[-1]['timestamp']
        return (time.time() - last_decision_time) < self.cooldown_period

    def _record_decision(self, person_id: str, result: SmartAttendanceResult):
        """Record a decision in the history

        Args:
            person_id: Person identifier
            result: Smart attendance result
        """
        if person_id not in self.decision_history:
            self.decision_history[person_id] = []

        decision_record = {
            'timestamp': result.timestamp,
            'access_decision': result.access_decision,
            'ppe_status': result.ppe_status,
            'missing_ppe': result.missing_ppe,
            'face_confidence': result.face_confidence
        }

        self.decision_history[person_id].append(decision_record)

        # Keep only last 10 decisions per person
        if len(self.decision_history[person_id]) > 10:
            self.decision_history[person_id] = self.decision_history[person_id][-10:]

        # Add to recent decisions (keep last 20)
        self.recent_decisions.append(result.to_dict())
        if len(self.recent_decisions) > 20:
            self.recent_decisions = self.recent_decisions[-20:]

    def _generate_message(self, access_decision: AccessDecision, person_name: str,
                         missing_ppe: List[str], ppe_status: Dict[str, bool]) -> str:
        """Generate user-friendly message based on decision

        Args:
            access_decision: The access decision
            person_name: Person's name
            missing_ppe: List of missing PPE items
            ppe_status: PPE status dictionary

        Returns:
            User-friendly message string
        """
        if access_decision == AccessDecision.GRANTED:
            return f"✅ Access Granted: All PPE Verified for {person_name}"

        elif access_decision == AccessDecision.DENIED:
            if missing_ppe:
                missing_items = ", ".join(missing_ppe)
                return f"❌ Access Denied: Please Wear {missing_items}"
            else:
                return f"❌ Access Denied: PPE Compliance Issue for {person_name}"

        elif access_decision == AccessDecision.UNKNOWN_PERSON:
            return "❓ Access Denied: Unknown Person - Please Register"

        elif access_decision == AccessDecision.NO_FACE_DETECTED:
            return "👤 No Face Detected - Please Position Yourself Clearly"

        return "⚠️ Access Decision Pending"

    def process_frame(self, ppe_detections: List[Dict], face_results: List[Dict]) -> Optional[SmartAttendanceResult]:
        """Process a frame for smart attendance decision

        Args:
            ppe_detections: PPE detection results from detection engine
            face_results: Face recognition results

        Returns:
            SmartAttendanceResult if a decision was made, None otherwise
        """
        if not self.enabled:
            return None

        current_time = time.time()

        # Check if we have face recognition results
        if not face_results:
            return SmartAttendanceResult(
                person_name="Unknown",
                person_id=None,
                face_confidence=0.0,
                access_decision=AccessDecision.NO_FACE_DETECTED,
                ppe_status={},
                missing_ppe=[],
                message=self._generate_message(AccessDecision.NO_FACE_DETECTED, "Unknown", [], {}),
                timestamp=current_time
            )

        # Process each recognized face
        for face in face_results:
            recognized_person = face.get('recognized_person')
            face_confidence = face.get('recognition_confidence', 0)
            person_id = face.get('person_id')

            # Skip if face recognition confidence is too low
            if face_confidence < 60:  # Minimum confidence threshold
                continue

            # Handle unknown person
            if not recognized_person:
                result = SmartAttendanceResult(
                    person_name="Unknown Person",
                    person_id=None,
                    face_confidence=face_confidence,
                    access_decision=AccessDecision.UNKNOWN_PERSON,
                    ppe_status={},
                    missing_ppe=[],
                    message=self._generate_message(AccessDecision.UNKNOWN_PERSON, "Unknown Person", [], {}),
                    timestamp=current_time
                )
                self.stats['unknown_persons'] += 1
                self.stats['total_decisions'] += 1
                return result

            # Check cooldown for known person
            if self._check_cooldown(str(person_id)):
                continue  # Skip if in cooldown

            # Analyze PPE compliance
            ppe_status, missing_ppe = self._analyze_ppe_compliance(ppe_detections)

            # Make access decision
            if not missing_ppe:
                access_decision = AccessDecision.GRANTED
                self.stats['access_granted'] += 1
            else:
                access_decision = AccessDecision.DENIED
                self.stats['access_denied'] += 1

            # Create result
            result = SmartAttendanceResult(
                person_name=recognized_person,
                person_id=str(person_id) if person_id else None,
                face_confidence=face_confidence,
                access_decision=access_decision,
                ppe_status=ppe_status,
                missing_ppe=missing_ppe,
                message=self._generate_message(access_decision, recognized_person, missing_ppe, ppe_status),
                timestamp=current_time
            )

            # Record the decision
            if result.person_id:
                self._record_decision(result.person_id, result)

            self.stats['total_decisions'] += 1

            return result

        return None

    def get_recent_decisions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent attendance decisions

        Args:
            limit: Maximum number of decisions to return

        Returns:
            List of recent decisions
        """
        return self.recent_decisions[-limit:] if self.recent_decisions else []

    def get_person_history(self, person_id: str) -> List[Dict[str, Any]]:
        """Get decision history for a specific person

        Args:
            person_id: Person identifier

        Returns:
            List of decisions for the person
        """
        return self.decision_history.get(person_id, [])

    def get_statistics(self) -> Dict[str, Any]:
        """Get smart attendance statistics

        Returns:
            Dictionary containing statistics
        """
        session_duration = time.time() - self.stats['session_start']

        stats = self.stats.copy()
        stats.update({
            'session_duration': session_duration,
            'decisions_per_minute': (self.stats['total_decisions'] / max(session_duration / 60, 1)),
            'access_granted_rate': (self.stats['access_granted'] / max(self.stats['total_decisions'], 1)) * 100,
            'access_denied_rate': (self.stats['access_denied'] / max(self.stats['total_decisions'], 1)) * 100,
            'unknown_person_rate': (self.stats['unknown_persons'] / max(self.stats['total_decisions'], 1)) * 100,
            'enabled': self.enabled,
            'ppe_requirements': self.ppe_requirements.get_required_items()
        })

        return stats

    def reset_statistics(self):
        """Reset all statistics"""
        self.stats = {
            'total_decisions': 0,
            'access_granted': 0,
            'access_denied': 0,
            'unknown_persons': 0,
            'session_start': time.time()
        }
        self.recent_decisions = []
        self.decision_history = {}
        logger.info("Smart PPE Attendance statistics reset")

    def should_update_attendance(self, result: SmartAttendanceResult) -> bool:
        """Determine if attendance should be updated based on the result

        Args:
            result: Smart attendance result

        Returns:
            True if attendance should be updated, False otherwise
        """
        # Only update attendance for granted access with known persons
        return (result.access_decision == AccessDecision.GRANTED and
                result.person_id is not None and
                result.face_confidence > 70)

    def get_attendance_status(self, result: SmartAttendanceResult) -> str:
        """Get attendance status based on smart attendance result

        Args:
            result: Smart attendance result

        Returns:
            Attendance status string
        """
        if result.access_decision == AccessDecision.GRANTED:
            return "Present"
        else:
            return "Absent"
